{"name": "k<PERSON>ri-portfolio", "version": "0.1.0", "private": true, "description": "Portfolio website of <PERSON> - Full Stack Developer and Computer Engineering Student", "author": "<PERSON>", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "preview": "next build && next start"}, "dependencies": {"@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@vercel/analytics": "^1.1.1", "@vercel/speed-insights": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "framer-motion": "^10.16.16", "khairi-portfolio": "file:", "lucide-react": "^0.303.0", "next": "^14.0.4", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-lazy-load-image-component": "^1.6.0", "sonner": "^1.3.1", "tailwind-merge": "^2.2.0", "typewriter-effect": "^2.22.0"}, "devDependencies": {"@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@types/react-lazy-load-image-component": "^1.6.3", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["portfolio", "nextjs", "react", "typescript", "tailwindcss", "framer-motion", "developer", "full-stack"]}